"use client";

import { useEffect, useState } from "react";
import {
  doc,
  collection,
  query,
  where,
  orderBy,
  limit,
} from "firebase/firestore";
import { useDocument, useCollection } from "react-firebase-hooks/firestore";
import {
  firestore,
  isFirestoreAvailable,
  onFirebaseReady,
  COLLECTIONS,
} from "../app/firebase/firebase";
import {
  PowerHour,
  PowerHourID,
  PowerHourGenerationSteps,
} from "../models/power-hour";

/**
 * Hook to fetch and watch a power hour document from Firestore
 * @param powerHourId The ID of the power hour to fetch
 * @returns Object containing the power hour data, loading state, and error
 */
export const useFirestorePowerHour = (powerHourId: PowerHourID) => {
  // Always define state hooks at the top level
  const [isReady, setIsReady] = useState(false);
  const [customError, setCustomError] = useState<Error | null>(null);

  // Server-side guard - return empty state if running on server
  if (typeof window === 'undefined') {
    return {
      powerHour: null,
      loading: true,
      error: null,
      connectionError: null,
      firestoreError: null
    };
  }

  // Always call useDocument with a valid reference, even if we're not ready yet
  // We'll create a ref that points to a non-existent document when not ready
  const docRef =
    isReady && isFirestoreAvailable() && powerHourId && firestore
      ? doc(firestore, COLLECTIONS.POWER_HOURS, powerHourId)
      : firestore
      ? doc(firestore, COLLECTIONS.POWER_HOURS, "placeholder-doc-id")
      : null;

  // Always call the hook, never conditionally
  const [snapshot, loading, firestoreError] = useDocument(docRef, {
    snapshotListenOptions: { includeMetadataChanges: false }
  });

  // Initialize Firebase on component mount
  useEffect(() => {
    // Clear any previous errors
    setCustomError(null);

    // Set custom error if no powerHourId is provided
    if (!powerHourId) {
      setCustomError(new Error("No power hour ID provided"));
      return;
    }

    // Initialize Firebase with error handling
    const initializeFirebase = () => {
      try {
        onFirebaseReady(() => {
          console.log("useFirestorePowerHour: Firebase ready");
          setIsReady(true);
          setCustomError(null); // Clear any previous connection errors
        });
      } catch (error) {
        console.error("useFirestorePowerHour: Firebase initialization failed:", error);
        setCustomError(error as Error);
        setIsReady(false);
      }
    };

    initializeFirebase();
  }, [powerHourId]);

  // Combine all error states
  const error = customError || firestoreError;

  // Determine actual loading state
  const isLoading =
    loading || !isReady || !isFirestoreAvailable() || !powerHourId;

  // Process the snapshot only when we're ready and have valid data
  let powerHour: PowerHour | null = null;
  if (!isLoading && snapshot?.exists() && !error) {
    powerHour = { id: snapshot.id, ...snapshot.data() } as PowerHour;
  } else if (!isLoading && powerHourId && !snapshot?.exists() && !error) {
    // Document doesn't exist
    console.warn(`useFirestorePowerHour: Power hour with ID ${powerHourId} not found`);
  }

  // Log errors for debugging
  if (error) {
    console.error("useFirestorePowerHour: Error detected:", {
      customError: customError?.message,
      firestoreError: firestoreError?.message,
      combinedError: error.message
    });
  }

  return {
    powerHour,
    loading: isLoading,
    error,
    connectionError: customError,
    firestoreError
  };
};

/**
 * Hook to check if a power hour is complete
 * @param powerHourId The ID of the power hour to check
 * @returns Object containing completion status, loading state, and error
 */
export const useIsPowerHourComplete = (powerHourId: PowerHourID) => {
  const { powerHour, loading, error } = useFirestorePowerHour(powerHourId);

  // Check if the power hour is complete
  const isComplete =
    powerHour?.currentStep === PowerHourGenerationSteps.Complete;

  return { isComplete, loading, error };
};

/**
 * Hook to check if a power hour has any errors
 * @param powerHourId The ID of the power hour to check
 * @returns Object containing error status, loading state, and error
 */
export const useHasPowerHourErrors = (powerHourId: PowerHourID) => {
  const { powerHour, loading, error } = useFirestorePowerHour(powerHourId);

  // Check if the power hour has errors
  const hasErrors = Boolean(powerHour?.error);

  // The error can be either a string or an object with a message property
  let errorMessage: string | null = null;
  if (powerHour?.error) {
    if (typeof powerHour.error === "string") {
      errorMessage = powerHour.error;
    } else if (
      typeof powerHour.error === "object" &&
      powerHour.error !== null
    ) {
      // Try to get message from error object if it exists
      errorMessage = (powerHour.error as any).message || "Unknown error";
    }
  }

  return { hasErrors, errorMessage, loading, error };
};

/**
 * Hook to fetch multiple power hours from Firestore
 * @param limit Maximum number of power hours to fetch
 * @returns Object containing the power hours array, loading state, and error
 */
export const useFirestorePowerHours = (limitCount?: number) => {
  // Always define state hooks at the top level
  const [isReady, setIsReady] = useState(false);
  const [connectionError, setConnectionError] = useState<Error | null>(null);

  // Server-side guard - return empty state if running on server
  if (typeof window === 'undefined') {
    return {
      powerHours: [],
      loading: true,
      error: null,
      connectionError: null,
      firestoreError: null
    };
  }

  // Create a query that's valid even if we're not ready yet
  let powerHoursQuery;
  if (isReady && isFirestoreAvailable() && firestore) {
    try {
      // Valid query when ready
      if (typeof limitCount === "number") {
        powerHoursQuery = query(
          collection(firestore, COLLECTIONS.POWER_HOURS),
          orderBy("createdAt", "desc"),
          limit(limitCount)
        );
      } else {
        powerHoursQuery = query(
          collection(firestore, COLLECTIONS.POWER_HOURS),
          orderBy("createdAt", "desc")
        );
      }
      console.log("useFirestorePowerHours: Query created successfully");
    } catch (error) {
      console.error("useFirestorePowerHours: Error creating query:", error);
      setConnectionError(error as Error);
      powerHoursQuery = null;
    }
  } else if (firestore) {
    try {
      // Fallback query for when we're not ready
      // This ensures the hook is always called with a valid reference
      powerHoursQuery = query(collection(firestore, COLLECTIONS.POWER_HOURS));
    } catch (error) {
      console.error("useFirestorePowerHours: Error creating fallback query:", error);
      setConnectionError(error as Error);
      powerHoursQuery = null;
    }
  } else {
    console.warn("useFirestorePowerHours: Firestore not available");
    setConnectionError(new Error("Firestore is not initialized or available"));
    powerHoursQuery = null;
  }

  // Initialize on component mount - always call useEffect
  useEffect(() => {
    console.log("useFirestorePowerHours: Initializing Firebase...");

    const initializeFirebase = () => {
      try {
        onFirebaseReady(() => {
          console.log("useFirestorePowerHours: Firebase ready, setting isReady to true");
          setIsReady(true);
          setConnectionError(null); // Clear any previous connection errors
        });
      } catch (error) {
        console.error("useFirestorePowerHours: Firebase initialization failed:", error);
        setConnectionError(error as Error);
        setIsReady(false);
      }
    };

    initializeFirebase();
  }, []);

  // Always call the hook, never conditionally
  // Note: react-firebase-hooks can handle null queries
  const [snapshot, loading, firestoreError] = useCollection(powerHoursQuery);

  // Determine actual loading state
  const isLoading = loading || !isReady || !isFirestoreAvailable();

  // Combine connection errors and Firestore errors
  const combinedError = connectionError || firestoreError;

  // Only process snapshot data when we're actually ready and no errors
  let powerHours: PowerHour[] = [];
  if (!isLoading && snapshot && !combinedError) {
    console.log("useFirestorePowerHours: Processing snapshot with", snapshot.docs.length, "documents");
    powerHours = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as PowerHour[];
    console.log("useFirestorePowerHours: Processed power hours:", powerHours.length);
  } else {
    console.log("useFirestorePowerHours: Not processing snapshot", {
      isLoading,
      hasSnapshot: !!snapshot,
      hasError: !!combinedError,
      errorMessage: combinedError?.message
    });
  }

  // Log errors for debugging
  if (combinedError) {
    console.error("useFirestorePowerHours: Error detected:", {
      connectionError: connectionError?.message,
      firestoreError: firestoreError?.message,
      combinedError: combinedError.message
    });
  }

  return {
    powerHours,
    loading: isLoading,
    error: combinedError,
    connectionError,
    firestoreError
  };
};
