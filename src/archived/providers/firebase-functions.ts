/*
 * ARCHIVED CODE - DO NOT USE
 * This file was archived on May 14, 2025
 * Reason: Redundant implementation replaced by the more focused n8n-provider.ts
 * The pipeline function in this provider was actually using the n8n webhook endpoint directly.
 * All components using this provider have been updated to use N8nProvider directly.
 */

import { toast } from "react-toastify";
import { PHOperations } from "../../models/enums";
import { PowerHour, PowerHourEntry, Song } from "@/models/power-hour";
import { PHActions } from "../../../functions/src/[models]/enums";

// Automatically use localhost for development
const isDevEnvironment = process.env.NODE_ENV === "development";
const baseUrl = isDevEnvironment
  ? "http://localhost:5001/power-hour-ai/us-central1"
  : "https://us-central1-power-hour-ai.cloudfunctions.net";

// API route for proxy in Next.js if needed to avoid CORS
const proxyBase = "/api/proxy?url=";

/**
 * Interface for error responses from Firebase Functions
 */
interface ErrorResponse {
  success: boolean;
  error: string;
  details?: any;
  errorCode?: string;
  step?: string;
  powerHourId?: string;
  timestamp: string;
  message?: string;
}

/**
 * Parse error response from Firebase function
 */
const parseErrorResponse = (error: any): ErrorResponse => {
  // Try to extract the error response from the error object
  if (error.response?.data) {
    return error.response.data;
  }

  // Fallback for network errors or other issues
  return {
    success: false,
    error: error.message || "Unknown error occurred",
    errorCode: "NETWORK_ERROR",
    timestamp: new Date().toISOString()
  };
};

/**
 * Helper function to handle errors from Firebase Functions
 */
const handleFunctionError = (error: any, powerHourId?: string): ErrorResponse => {
  const errorResponse = parseErrorResponse(error);

  // Display toast notification with error details
  toast.error(`Error: ${errorResponse.error}${errorResponse.step ? ` in step: ${errorResponse.step}` : ''}`);

  console.error("Firebase function error:", errorResponse);

  return errorResponse;
};

/**
 * Firebase Functions API with improved error handling
 */
export const FirebaseFunctions = {
  /**
   * Execute a pipeline of operations
   */
  pipeline: async (
    payload: { search?: string; disallow?: any, powerHourId?: string, count?: number },
    action: any,
    tasks?: PHOperations[]
  ) => {
    try {
      // Use n8n webhook endpoint for power hour creation
      const endpoint = `https://n8n-pve.ensoq.ddns.net/webhook/createPowerHour`;

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          search: payload.search,
          count: payload.count
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw { response: { data: errorData }, status: response.status };
      }

      const data = await response.json();
      toast.success("Tasks completed successfully");
      return data;
    } catch (error) {
      const errorResponse = handleFunctionError(error, payload.powerHourId);

      // Return a structured error that can be handled by the UI
      return { error: errorResponse };
    }
  },

  /**
   * Validate videos for a list of video IDs
   */
  validateVideos: async (videoIds: string[]) => {
    try {
      const endpoint = `${baseUrl}/validateVideos`;

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          videoIds
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw { response: { data: errorData }, status: response.status };
      }

      return await response.json();
    } catch (error) {
      const errorResponse = handleFunctionError(error);
      return { error: errorResponse };
    }
  }
};