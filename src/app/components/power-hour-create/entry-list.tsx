import {
	PowerHour,
	PowerHourEntry,
	PowerHourID,
	Song,
	VideoStatus,
} from "@/models/power-hour";
import { N8nProvider } from "@/app/providers/n8n-provider";
import React, { useState } from "react";
import { AiOutlineMinusCircle } from "react-icons/ai";
import { FaCheck } from "react-icons/fa";
import { useRouter } from "next/navigation";
import { PHOperations } from "@/models/enums";
import Loading from "@/app/power-hour-ai/loading";
import { toast } from "react-toastify";
import { PHActions } from "../../../../functions/src/[models]/enums";

// Define interfaces for Firestore data structure compatibility
interface ExtendedVideo {
	thumbnail?: string;
	id?: string;
	link?: string;
	title?: string;
}

interface EntryListProps {
	search: string;
	powerHourId: PowerHourID;
	entries: PowerHourEntry[];
	updateEntries: (entries: PowerHourEntry[]) => void;
}

const EntryList: React.FC<EntryListProps> = ({
	search,
	powerHourId,
	entries,
	updateEntries,
}) => {
	const [isLoading, setIsLoading] = useState(false);
	const [isReplacing, setIsReplacing] = useState<boolean>(false);

	const router = useRouter();

	const toggleMarkForReplacement = (id: string | number) => {
		const updatedEntries = entries.map((entry) => {
			// Check for different possible ID fields
			const entryVideo = entry.video as ExtendedVideo | undefined;
			if (
				entry.id === id || 
				(entry.song && entry.song.id === id) || 
				(entryVideo && entryVideo.id === id)
			) {
				return { ...entry, markedForReplacement: !entry.markedForReplacement };
			}
			return entry;
		});
		updateEntries(updatedEntries);
	};

	const replaceEntries = () => {
		setIsReplacing(true);
		const badSongs = entries
			.filter((entry) => entry.markedForReplacement)
			.map((entry) => entry.song);

		console.log(`Replacing ${badSongs.length} songs in power hour`);
		
		// When n8n call is implemented, we'll pass the specific songs to replace
		// For now, use the existing logic to generate new entries
		// Use the number of bad songs as the count for replacement
		N8nProvider.createPowerHour(search, badSongs.length).then((result: { powerHourId?: string, entries: PowerHourEntry[] }) => {
			// Take only the number of entries we need to replace
			const replacementEntries = result.entries.slice(0, badSongs.length);
			
			// Generate unique IDs for new entries to avoid React key warnings
			const newEntries = replacementEntries.map((entry) => ({
				...entry,
				id: `new-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
				isReplacement: true,
			}));
			
			// Filter out entries marked for replacement and add new ones
			const updatedEntries = entries
				.filter((entry) => !entry.markedForReplacement)
				.concat(newEntries);
			
			console.log('Updated entries:', updatedEntries);
			updateEntries(updatedEntries);
			setIsReplacing(false); // Stop loading
			toast.success(`${newEntries.length} songs replaced successfully!`); // Show success message
		}).catch((error) => {
			console.error('Error replacing songs:', error);
			setIsReplacing(false);
			toast.error(`Error replacing songs: ${error.message || error}`);
		});
	};

	const anyMarkedForReplacement = entries?.some(
		(entry) => entry.markedForReplacement
	);

	const anyDontHaveVideo = entries?.some(
		(entry) => {
			const entryVideo = entry.video as ExtendedVideo | undefined;
			return !entryVideo && entry.videoStatus?.status?.toLowerCase() !== "available";
		}
	);

	return (
		<div className="flex flex-col h-full items-center bg-gray-800 text-white overflow-hidden">
			{isLoading || isReplacing ? (
				<div className="w-full h-full flex items-center justify-center bg-gray-800">
					<Loading />
				</div>
			) : entries?.length > 0 ? (
				<div className="overflow-auto flex-grow w-full">
					<div className="p-4 w-full grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
						{entries.map((entry) => {
							// Parse Firestore data structure
							const entryVideo = entry.video as ExtendedVideo | undefined;
							const backupVideo = (entry as any).backup_video as ExtendedVideo | undefined;
							
							// Get thumbnail from video or backup_video
							const thumbnail = entryVideo?.thumbnail || 
								backupVideo?.thumbnail || 
								entry.song?.thumbnailUrl;
							
							// Get song name from song.name (Firestore) or song.title (legacy)
							const songTitle = (entry.song as any)?.name || entry.song?.title || 'Unknown Song';
							
							// Get artist name
							const artistName = entry.song?.artist || 'Unknown Artist';
							
							// Get release year if available
							const releaseYear = (entry.song as any)?.release;
							
							// Check video availability
							const hasVideo = !!entryVideo || (entry.videoStatus?.status?.toLowerCase() === "available");
							
							// Generate unique ID
							const entryId = entry.id || entry.song?.id || `entry-${Math.random()}`;
							
							return (
								<div
									key={entryId}
									className="relative group cursor-pointer bg-gray-900 rounded-lg overflow-hidden shadow-lg transition-all duration-300 hover:shadow-xl"
									onClick={() => toggleMarkForReplacement(entryId)}
									style={{ height: '325px' }}
								>
									{/* Thumbnail Image */}
									<div className="w-full h-48 overflow-hidden">
										{thumbnail ? (
											<img 
												src={thumbnail} 
												alt={songTitle}
												className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
												onError={(e) => {
													// Fallback for broken images
													(e.target as HTMLImageElement).src = '/placeholder-thumbnail.jpg';
												}}
											/>
										) : (
											// Display a placeholder if no thumbnail URL
											<div className="w-full h-full bg-gray-700 flex items-center justify-center">
												<span className="text-gray-400 text-lg">No Image</span>
											</div>
										)}
									</div>

									{/* Song Info */}
									<div className="p-4">
										<h3 className="font-bold text-white text-sm line-clamp-2 mb-1" title={songTitle}>
											{songTitle}
										</h3>
										<p className="text-gray-300 text-xs line-clamp-1" title={artistName}>
											{artistName}
										</p>
										{releaseYear && (
											<p className="text-gray-400 text-xs italic mt-1">
												{releaseYear}
											</p>
										)}
										
										{/* Video Status Indicator */}
										<div className="absolute bottom-3 right-3 flex items-center">
											{hasVideo ? (
												<span className="text-green-500 flex items-center">
													<FaCheck className="mr-1" /> Video Ready
												</span>
											) : (
												<span className="text-red-500 flex items-center">
													<AiOutlineMinusCircle className="mr-1" /> No Video
												</span>
											)}
										</div>
									</div>

									{/* Hover Overlay */}
									<div className="absolute top-0 left-0 w-full h-full bg-black bg-opacity-50 flex items-center justify-center rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300">
										<span className="text-white text-lg font-bold">
											{entry?.markedForReplacement ? "Keep This Song" : "Replace This Song"}
										</span>
									</div>

									{/* Status Badges */}
									{entry?.markedForReplacement && (
										<div className="absolute top-0 right-0 p-2 bg-red-500 text-white font-bold rounded-bl-lg">
											Replace
										</div>
									)}

									{entry?.isReplacement && !entry?.markedForReplacement && (
										<div className="absolute top-0 right-0 p-2 bg-green-500 text-white font-bold rounded-bl-lg">
											New
										</div>
									)}
								</div>
							);
						})}
					</div>
				</div>
			) : (
				<div className="w-full h-full flex items-center justify-center bg-gray-800">No songs available.</div>
			)}
			<div className="w-full bg-gray-900 p-4 shadow-2xl">
				<div className="text-center">
					{anyDontHaveVideo || anyMarkedForReplacement ? (
						<button
							onClick={replaceEntries}
							disabled={isReplacing}
							className={`mx-auto px-6 py-2 rounded-md ${
								anyMarkedForReplacement
									? "bg-red-500 hover:bg-red-600"
									: "bg-green-500 hover:bg-green-600"
							} ${isReplacing ? "opacity-50 cursor-not-allowed" : ""}`}
						>
							{isReplacing ? (
								<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mx-auto"></div>
							) : anyMarkedForReplacement ? (
								"Replace"
							) : (
								"Validate"
							)}
						</button>
					) : (
						<button
							onClick={() =>
								router.push(
									`/power-hour-ai/active-power-hour?powerHourId=${powerHourId}`
								)
							}
							className="mx-auto px-6 py-2 rounded-md bg-green-500 hover:bg-green-600"
						>
							Looks Good
						</button>
					)}
				</div>
			</div>
		</div>
	);
};

export default EntryList;
